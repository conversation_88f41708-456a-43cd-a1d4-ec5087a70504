#!/bin/bash

# 简单的去重脚本
# 使用方法: ./simple_dedupe.sh

INPUT_FILE="code.md"
OUTPUT_FILE="code_unique.md"
BACKUP_FILE="code_backup.md"

echo "🔍 开始去重处理..."

# 检查文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo "❌ 错误: 找不到 $INPUT_FILE 文件"
    exit 1
fi

# 统计原始数量
ORIGINAL_COUNT=$(wc -l < "$INPUT_FILE")
echo "📊 原始兑换码数量: $ORIGINAL_COUNT"

# 创建备份
echo "💾 创建备份文件..."
cp "$INPUT_FILE" "$BACKUP_FILE"

# 去重 - 保持首次出现的顺序
echo "🔄 正在去重..."
awk '!seen[$0]++' "$INPUT_FILE" > "$OUTPUT_FILE"

# 统计去重后数量
UNIQUE_COUNT=$(wc -l < "$OUTPUT_FILE")
DUPLICATE_COUNT=$((ORIGINAL_COUNT - UNIQUE_COUNT))

echo "✅ 去重完成!"
echo "=================================="
echo "原始数量: $ORIGINAL_COUNT"
echo "去重后数量: $UNIQUE_COUNT"
echo "移除重复: $DUPLICATE_COUNT"
echo "=================================="

# 显示前10个去重后的兑换码
echo "📋 去重后的前10个兑换码:"
head -10 "$OUTPUT_FILE"

echo ""
echo "💡 文件说明:"
echo "   原始文件备份: $BACKUP_FILE"
echo "   去重后文件: $OUTPUT_FILE"
echo ""
echo "🔄 如果确认无误，可以替换原文件:"
echo "   mv $OUTPUT_FILE $INPUT_FILE"
