# 🔍 调试指南

## 问题排查

根据console输出，我发现了几个问题并进行了修复：

### 1. 重复注入问题
**问题**: `Identifier 'isPageReady' has already been declared`
**原因**: content script被重复注入
**修复**: 添加了防重复注入检查

### 2. 成功状态误检测
**问题**: 插件误检测到成功图标
**原因**: 成功图标一直存在但是隐藏的，需要检查容器的opacity
**修复**: 改进了成功状态检测逻辑

### 3. 错误消息检测不准确
**问题**: 无法正确检测到"无效的促销代码"消息
**修复**: 添加了多种检测机制

## 🔧 修复内容

### 防重复注入
```javascript
// 防止重复注入
if (window.perplexityValidatorLoaded) {
    console.log('Content script already loaded, skipping...');
} else {
    window.perplexityValidatorLoaded = true;
}
```

### 改进的成功检测
```javascript
// 检查成功图标的父容器的opacity
let parentContainer = successIcon.closest('div');
while (parentContainer && !parentContainer.classList.contains('absolute')) {
    parentContainer = parentContainer.parentElement;
}

// 只有当包含成功图标的绝对定位容器opacity > 0.8时才认为是真正的成功
if (opacity > 0.8) {
    // 检测到成功
}
```

### 多重错误检测机制
1. **MutationObserver检测新增节点**
2. **定期检查页面内容**
3. **多种CSS选择器**

## 🧪 测试步骤

### 1. 重新加载插件
1. 打开 `chrome://extensions/`
2. 找到插件，点击"重新加载"按钮
3. 刷新Perplexity页面

### 2. 开启调试模式
1. 在插件中勾选"显示调试信息"
2. 观察调试面板中的日志

### 3. 测试错误检测
1. 使用一个明显无效的兑换码（如"INVALID123"）
2. 观察是否能正确检测到错误消息
3. 检查是否自动切换到下一个

### 4. 观察console输出
应该看到类似的日志：
```
Perplexity Code Validator content script loaded
初始化content script
自动切换模式: 开启
检测到错误消息: 无效的促销代码。请尝试复制并粘贴代码，并检查是否有任何拼写错误。
自动切换到下一个兑换码
```

## 🔍 调试技巧

### 1. 手动检查错误消息
在浏览器console中运行：
```javascript
// 检查页面中是否有错误消息
const pageText = document.body.textContent;
console.log('页面包含无效消息:', pageText.includes('无效的促销代码'));

// 查找错误元素
const errorElements = document.querySelectorAll('.text-superAlt');
errorElements.forEach(el => {
    if (el.textContent.includes('无效')) {
        console.log('找到错误元素:', el, '文本:', el.textContent);
    }
});
```

### 2. 检查成功图标状态
```javascript
// 检查成功图标
const successIcon = document.querySelector('svg[data-icon="circle-check"]');
if (successIcon) {
    const container = successIcon.closest('div.absolute');
    console.log('成功图标:', successIcon);
    console.log('容器:', container);
    console.log('容器opacity:', window.getComputedStyle(container).opacity);
}
```

### 3. 手动触发自动切换
```javascript
// 手动触发下一个兑换码
if (window.handleNextCode) {
    window.handleNextCode();
}
```

## 🚨 常见问题

### 问题1: 自动切换不工作
**可能原因**:
- 自动切换模式被关闭
- 错误消息检测失败
- 只有一个兑换码

**解决方案**:
1. 检查设置中"无效时自动切换下一个"是否勾选
2. 开启调试模式查看检测日志
3. 确保有多个兑换码

### 问题2: 重复检测成功状态
**可能原因**:
- 成功图标一直存在但隐藏
- 检测逻辑过于宽松

**解决方案**:
- 已修复：现在检查容器的opacity
- 添加了更严格的检测条件

### 问题3: 错误消息检测不到
**可能原因**:
- 页面结构变化
- CSS选择器不匹配
- 错误消息出现时间太短

**解决方案**:
- 添加了定期检查机制
- 使用多种检测方法
- 增加了文本内容检查

## 📋 调试清单

测试前请确认：
- [ ] 插件已重新加载
- [ ] 页面已刷新
- [ ] 调试模式已开启
- [ ] 自动切换模式已开启
- [ ] 有多个兑换码可供测试

测试过程中观察：
- [ ] console中没有重复声明错误
- [ ] 能正确检测到错误消息
- [ ] 自动切换功能正常工作
- [ ] 通知提示正常显示

## 🔄 如果问题仍然存在

1. **完全重新安装插件**
   - 删除插件
   - 重新加载插件文件夹

2. **清除浏览器缓存**
   - 清除Perplexity网站的缓存
   - 重新打开页面

3. **检查页面结构**
   - Perplexity可能更新了页面结构
   - 需要更新选择器

4. **提供详细日志**
   - 开启调试模式
   - 复制完整的console输出
   - 描述具体的操作步骤

这个修复版本应该能解决自动切换不工作的问题。请重新加载插件并测试！
