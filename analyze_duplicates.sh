#!/bin/bash

# 分析重复兑换码的详细脚本
# 使用方法: ./analyze_duplicates.sh

INPUT_FILE="code.md"

echo "🔍 分析重复兑换码..."
echo "=================================="

if [ ! -f "$INPUT_FILE" ]; then
    echo "❌ 错误: 找不到 $INPUT_FILE 文件"
    exit 1
fi

# 统计总数
TOTAL_COUNT=$(wc -l < "$INPUT_FILE")
echo "📊 总兑换码数量: $TOTAL_COUNT"

# 统计唯一数量
UNIQUE_COUNT=$(sort "$INPUT_FILE" | uniq | wc -l)
echo "📊 唯一兑换码数量: $UNIQUE_COUNT"

DUPLICATE_COUNT=$((TOTAL_COUNT - UNIQUE_COUNT))
echo "📊 重复兑换码数量: $DUPLICATE_COUNT"

echo ""
echo "🔍 重复兑换码详情:"
echo "=================================="

# 找出重复的兑换码并显示出现次数
sort "$INPUT_FILE" | uniq -c | sort -nr | while read count code; do
    if [ "$count" -gt 1 ]; then
        echo "   $code (出现 $count 次)"
    fi
done

echo ""
echo "📋 重复次数统计:"
echo "=================================="

# 统计不同重复次数的分布
sort "$INPUT_FILE" | uniq -c | awk '{print $1}' | sort -n | uniq -c | while read freq count; do
    if [ "$count" -gt 1 ]; then
        echo "   出现 $count 次的兑换码有 $freq 个"
    fi
done

echo ""
echo "✅ 分析完成!"
