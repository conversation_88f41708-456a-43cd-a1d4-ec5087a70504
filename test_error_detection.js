// 测试错误检测的简化脚本
// 在浏览器console中运行这个脚本来测试错误检测

console.log('开始测试错误检测...');

// 1. 检查页面中是否有错误消息
function checkForErrorMessages() {
    console.log('=== 检查错误消息 ===');
    
    const errorKeywords = [
        '无效的促销代码',
        '请尝试复制并粘贴代码',
        '拼写错误',
        'invalid',
        'error'
    ];
    
    const allElements = document.querySelectorAll('*');
    let foundErrors = [];
    
    for (const element of allElements) {
        const text = element.textContent;
        if (text) {
            for (const keyword of errorKeywords) {
                if (text.includes(keyword) && 
                    element.offsetParent !== null &&
                    text.length < 200) {
                    
                    foundErrors.push({
                        keyword: keyword,
                        text: text.trim(),
                        element: element,
                        visible: element.offsetParent !== null,
                        textLength: text.length
                    });
                }
            }
        }
    }
    
    console.log('找到的错误消息:', foundErrors);
    return foundErrors;
}

// 2. 检查成功状态
function checkForSuccessState() {
    console.log('=== 检查成功状态 ===');
    
    // 检查成功图标
    const successIcon = document.querySelector('svg[data-icon="circle-check"]');
    if (successIcon) {
        let parentContainer = successIcon.closest('div');
        while (parentContainer && !parentContainer.classList.contains('absolute')) {
            parentContainer = parentContainer.parentElement;
        }
        
        if (parentContainer) {
            const opacity = window.getComputedStyle(parentContainer).opacity;
            console.log('成功图标容器opacity:', opacity);
            console.log('成功图标容器:', parentContainer);
        }
    }
    
    // 检查成功文本
    const xpath = "//div[contains(text(), '优惠码已应用') and string-length(text()) < 50]";
    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
    const successElement = result.singleNodeValue;
    
    if (successElement) {
        console.log('找到成功文本元素:', successElement);
        console.log('成功文本:', successElement.textContent);
        console.log('元素可见:', successElement.offsetParent !== null);
    } else {
        console.log('未找到成功文本元素');
    }
}

// 3. 模拟自动切换
function simulateAutoSwitch() {
    console.log('=== 模拟自动切换 ===');
    
    const errors = checkForErrorMessages();
    if (errors.length > 0) {
        console.log('检测到错误，应该触发自动切换');
        console.log('错误详情:', errors[0]);
        
        // 这里应该触发自动切换逻辑
        if (window.handleNextCode) {
            console.log('调用handleNextCode函数');
            window.handleNextCode();
        } else {
            console.log('handleNextCode函数不存在');
        }
    } else {
        console.log('未检测到错误消息');
    }
}

// 4. 监听页面变化
function setupTestListener() {
    console.log('=== 设置测试监听器 ===');
    
    const observer = new MutationObserver((mutations) => {
        console.log('页面发生变化:', mutations.length, '个变化');
        
        for (const mutation of mutations) {
            if (mutation.type === 'childList') {
                for (const node of mutation.addedNodes) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const text = node.textContent || '';
                        if (text.includes('无效的促销代码') || text.includes('请尝试复制并粘贴代码')) {
                            console.log('检测到新的错误消息:', text);
                            console.log('错误节点:', node);
                        }
                    }
                }
            }
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style', 'opacity']
    });
    
    console.log('测试监听器已设置');
    return observer;
}

// 运行所有测试
function runAllTests() {
    console.log('🔍 开始运行所有测试...');
    
    checkForErrorMessages();
    checkForSuccessState();
    simulateAutoSwitch();
    
    const observer = setupTestListener();
    
    console.log('✅ 所有测试完成');
    console.log('💡 现在可以手动提交一个无效的兑换码来测试检测功能');
    
    return observer;
}

// 自动运行测试
const testObserver = runAllTests();

// 提供手动测试函数
window.testErrorDetection = {
    checkErrors: checkForErrorMessages,
    checkSuccess: checkForSuccessState,
    simulate: simulateAutoSwitch,
    runAll: runAllTests,
    observer: testObserver
};

console.log('🎯 测试脚本已加载，可以使用 window.testErrorDetection 进行手动测试');
