# 🚀 批量检测功能说明

## 功能概述

新增的批量检测功能允许用户通过直接调用Perplexity API来并发检测多个兑换码，快速筛选出有效的兑换码，大大提高验证效率。

## ✨ 主要特性

### 1. 高效并发检测
- **并发处理**: 支持5-20个兑换码同时检测
- **批次处理**: 自动分批处理，避免服务器压力
- **智能延迟**: 批次间自动延迟，防止请求过于频繁

### 2. 实时进度显示
- **进度条**: 可视化显示检测进度
- **状态更新**: 实时显示当前检测状态
- **结果统计**: 显示有效和无效兑换码数量

### 3. 自动结果处理
- **智能筛选**: 自动保留有效兑换码
- **列表更新**: 无效兑换码自动移除
- **状态同步**: 与插件其他功能保持同步

## 🔧 技术实现

### API调用
```javascript
// 直接调用Perplexity API
const apiUrl = `https://www.perplexity.ai/rest/billing/get-coupon-metadata?discount_code=${code}&version=2.18&source=default`;

const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
        'referer': 'https://www.perplexity.ai/join/p/priority',
        'x-app-apiclient': 'default',
        'x-app-apiversion': '2.18',
        // ... 其他必要的headers
    },
    credentials: 'include'
});
```

### 并发控制
```javascript
// 分批处理兑换码
for (let i = 0; i < totalCodes; i += batchSize) {
    const batch = codes.slice(i, i + batchSize);
    
    // 并发检测当前批次
    const batchPromises = batch.map(code => checkSingleCode(code));
    const batchResults = await Promise.allSettled(batchPromises);
    
    // 批次间延迟
    await new Promise(resolve => setTimeout(resolve, 500));
}
```

### 结果判断
```javascript
// 根据API响应判断兑换码有效性
const isValid = data.status === 'success' && !data.error_code;

// 有效响应示例
{
    "status": "success",
    "name": "O2",
    "coupon_percent": 100.0,
    "error_code": null
}

// 无效响应示例
{
    "status": "failed",
    "error_code": "ALREADY_REDEEMED"
}
```

## 📋 使用方法

### 基本流程
1. **输入兑换码**: 在文本框中输入所有兑换码（每行一个）
2. **保存兑换码**: 点击"保存兑换码"按钮
3. **设置并发数**: 选择合适的并发数量（5-20个）
4. **开始检测**: 点击"批量检测"按钮
5. **查看结果**: 观察进度条和结果统计
6. **使用有效码**: 检测完成后只保留有效兑换码

### 并发数量选择
- **5个**: 保守模式，适合网络较慢或服务器敏感时
- **10个**: 推荐模式，平衡效率和稳定性
- **15个**: 快速模式，适合网络良好时
- **20个**: 极速模式，可能触发限制，谨慎使用

## 🎯 优势对比

### 传统方式 vs 批量检测

| 特性 | 传统页面验证 | 批量检测 |
|------|-------------|----------|
| 速度 | 每个兑换码需要手动操作 | 10个兑换码并发检测 |
| 效率 | 低，需要逐个验证 | 高，一次性筛选 |
| 用户操作 | 频繁点击和等待 | 一键启动，自动完成 |
| 错误处理 | 手动判断和处理 | 自动识别和移除 |
| 适用场景 | 少量兑换码 | 大量兑换码筛选 |

## 🛡️ 安全考虑

### 请求频率控制
- **批次延迟**: 每批次间500ms延迟
- **并发限制**: 最大20个并发请求
- **错误重试**: 失败请求不自动重试，避免过度请求

### Cookie和认证
- **自动携带**: 使用当前页面的cookies
- **同源请求**: 确保请求来源正确
- **Headers完整**: 包含所有必要的请求头

### 错误处理
- **网络错误**: 自动标记为无效，不影响其他检测
- **API限制**: 遇到限制时停止检测，显示错误信息
- **超时处理**: 单个请求超时自动标记为失败

## 📊 性能指标

### 检测速度
- **单个兑换码**: ~200-500ms
- **10个并发**: ~1-2秒完成一批
- **100个兑换码**: 约10-20秒完成全部检测

### 资源使用
- **内存占用**: 增加约1-2MB
- **网络流量**: 每个兑换码约1KB
- **CPU使用**: 轻微增加，主要用于JSON解析

## 🔍 调试和监控

### 调试模式
开启调试模式可以看到：
- 每个API请求的详细信息
- 响应数据的完整内容
- 检测过程的实时状态
- 错误信息和失败原因

### 监控信息
```javascript
// 调试日志示例
[14:30:15] 开始API检测兑换码: PPLXO2TVDTW9B3
[14:30:15] API响应: {"status":"failed","error_code":"ALREADY_REDEEMED"}
[14:30:15] 兑换码 PPLXO2TVDTW9B3 无效: ALREADY_REDEEMED
```

## 🚨 注意事项

### 使用限制
1. **需要登录**: 必须在已登录的Perplexity页面使用
2. **网络要求**: 需要稳定的网络连接
3. **频率限制**: 避免过于频繁的大批量检测

### 最佳实践
1. **合理并发**: 根据网络情况选择合适的并发数
2. **分批检测**: 大量兑换码建议分多次检测
3. **错误处理**: 遇到错误时检查网络和登录状态

### 故障排除
1. **检测失败**: 检查是否在Perplexity页面且已登录
2. **网络错误**: 检查网络连接和防火墙设置
3. **API限制**: 等待一段时间后重试，或减少并发数

## 🔄 与现有功能的集成

### 无缝集成
- **不影响现有功能**: 批量检测是独立的增强功能
- **结果同步**: 检测结果自动同步到其他功能
- **状态保持**: 与页面验证功能状态一致

### 工作流程建议
1. **大量兑换码**: 先使用批量检测筛选
2. **少量兑换码**: 直接使用页面验证
3. **混合使用**: 批量检测后再用页面验证确认

这个批量检测功能大大提高了兑换码验证的效率，特别适合需要处理大量兑换码的场景。
