#!/usr/bin/env python3
"""
Simple script to create PNG icons from SVG using cairosvg
If cairosvg is not available, creates simple colored squares as fallback
"""

import os
import sys

def create_fallback_icons():
    """Create simple fallback icons using HTML5 Canvas via a browser"""
    html_content = '''
<!DOCTYPE html>
<html>
<head>
    <title>Create Icons</title>
</head>
<body>
    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Draw background
            ctx.fillStyle = '#1F1F1F';
            ctx.fillRect(0, 0, size, size);
            
            // Draw white P-like shape (simplified Perplexity logo)
            ctx.fillStyle = '#FFFFFF';
            const scale = size / 128;
            
            // Simple P shape
            ctx.fillRect(20 * scale, 20 * scale, 60 * scale, 10 * scale);
            ctx.fillRect(20 * scale, 20 * scale, 10 * scale, 88 * scale);
            ctx.fillRect(20 * scale, 50 * scale, 40 * scale, 10 * scale);
            ctx.fillRect(50 * scale, 30 * scale, 10 * scale, 30 * scale);
            
            // Download the icon
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // Create icons
        [16, 48, 128].forEach(size => {
            setTimeout(() => createIcon(size), 100 * size);
        });
        
        document.body.innerHTML = '<h1>Icons created! Check your downloads folder.</h1>';
    </script>
</body>
</html>
    '''
    
    with open('create_icons.html', 'w') as f:
        f.write(html_content)
    
    print("Created create_icons.html")
    print("Open this file in a browser to generate PNG icons")

def main():
    try:
        import cairosvg
        
        # Read the SVG file
        with open('icons/icon.svg', 'r') as f:
            svg_content = f.read()
        
        # Create PNG icons in different sizes
        sizes = [16, 48, 128]
        
        for size in sizes:
            output_path = f'icons/icon{size}.png'
            cairosvg.svg2png(
                bytestring=svg_content.encode('utf-8'),
                write_to=output_path,
                output_width=size,
                output_height=size
            )
            print(f"Created {output_path}")
            
    except ImportError:
        print("cairosvg not available, creating fallback HTML generator...")
        create_fallback_icons()
    except Exception as e:
        print(f"Error creating icons: {e}")
        create_fallback_icons()

if __name__ == "__main__":
    main()
