// Background script for Perplexity Code Validator
console.log('Perplexity Code Validator background script loaded');

// 安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
    console.log('插件已安装/更新:', details.reason);
    
    if (details.reason === 'install') {
        // 首次安装时的欢迎消息
        chrome.tabs.create({
            url: 'https://www.perplexity.ai/join/p/priority'
        });
    }
});

// 处理来自content script和popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background收到消息:', message);
    
    switch (message.action) {
        case 'codeValidated':
            // 转发验证结果到popup（如果打开的话）
            chrome.runtime.sendMessage(message).catch(() => {
                // popup可能没有打开，忽略错误
            });
            break;
            
        case 'openPerplexityPage':
            // 打开Perplexity兑换页面
            chrome.tabs.create({
                url: 'https://www.perplexity.ai/join/p/priority'
            });
            break;
            
        case 'getTabInfo':
            // 获取当前标签页信息
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                if (tabs[0]) {
                    sendResponse({
                        url: tabs[0].url,
                        title: tabs[0].title,
                        isPerplexityPage: tabs[0].url.includes('perplexity.ai/join/p/priority')
                    });
                }
            });
            return true; // 保持消息通道开放
    }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当页面完全加载且是Perplexity兑换页面时，注入content script
    if (changeInfo.status === 'complete' && 
        tab.url && 
        tab.url.includes('perplexity.ai/join/p/priority')) {
        
        console.log('Perplexity兑换页面已加载，准备注入content script');
        
        // 确保content script已注入
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['content.js']
        }).catch((error) => {
            console.log('Content script可能已经注入:', error.message);
        });
    }
});

// 处理存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    console.log('存储发生变化:', changes, namespace);
    
    // 可以在这里添加数据同步逻辑
    if (changes.codes) {
        console.log('兑换码列表已更新');
    }
});

// 错误处理
chrome.runtime.onSuspend.addListener(() => {
    console.log('Background script即将被挂起');
});

// 启动时的清理工作
chrome.runtime.onStartup.addListener(() => {
    console.log('Chrome启动，background script重新加载');
});
