# 🚀 快速测试指南

## 语法错误已修复！

刚刚修复了 `Illegal return statement` 错误，现在插件应该能正常工作了。

## 🔧 立即测试步骤

### 1. 重新加载插件
```
1. 打开 chrome://extensions/
2. 找到插件，点击"重新加载"按钮
3. 完全刷新Perplexity页面 (Ctrl+F5)
```

### 2. 检查console
现在应该看到：
```
Perplexity Code Validator content script loaded
初始化content script
页面准备就绪 - 找到输入框和提交按钮
Content script初始化完成
```

**不应该再有**：
- `Identifier 'isPageReady' has already been declared`
- `Illegal return statement`

### 3. 测试自动切换
1. **准备测试兑换码**：
   ```
   INVALID123
   TESTCODE456
   PPLXO2E86B0OM4  (你的真实兑换码)
   ```

2. **设置插件**：
   - ✅ 勾选"显示调试信息"
   - ✅ 勾选"自动点击继续按钮"
   - ✅ 勾选"无效时自动切换下一个"

3. **开始测试**：
   - 输入测试兑换码并保存
   - 点击"开始验证"
   - 观察是否自动处理无效兑换码

## 📊 预期的正确流程

### Console输出应该是：
```
收到消息: {action: 'fillCode', code: 'INVALID123', ...}
尝试填充兑换码: INVALID123
找到输入框: input[placeholder="优惠码"]
兑换码已填充: INVALID123
自动点击继续按钮
已自动点击继续按钮
提交后检测到错误: 无效的促销代码。请尝试复制并粘贴代码，并检查是否有任何拼写错误。
自动切换到下一个兑换码
移除无效兑换码: INVALID123
填充下一个兑换码: TESTCODE456
```

### 页面上应该看到：
- 右上角出现红色通知："兑换码无效，已自动切换到下一个兑换码"
- 输入框自动填充新的兑换码
- 自动点击继续按钮

## 🔍 如果还有问题

### 检查项目：
1. **Console错误**：应该没有语法错误了
2. **重复注入**：应该只看到一次"content script loaded"
3. **错误检测**：使用明显无效的兑换码测试

### 手动测试错误检测：
在console中运行：
```javascript
// 检查当前页面是否有错误消息
const pageText = document.body.textContent;
console.log('页面包含无效消息:', pageText.includes('无效的促销代码'));

// 查找所有可能的错误元素
document.querySelectorAll('*').forEach(el => {
    const text = el.textContent;
    if (text && text.includes('无效的促销代码') && text.length < 200) {
        console.log('找到错误元素:', el, '文本:', text);
    }
});
```

## 💡 调试技巧

### 1. 强制触发自动切换
如果自动切换不工作，可以在console中手动触发：
```javascript
// 手动触发下一个兑换码
if (window.handleNextCode) {
    window.handleNextCode();
} else {
    console.log('handleNextCode函数不存在');
}
```

### 2. 检查设置状态
```javascript
// 检查自动切换设置
console.log('自动切换模式:', window.autoSwitchMode);
console.log('自动点击模式:', window.autoClickMode);
console.log('当前兑换码:', window.currentCode);
console.log('所有兑换码:', window.allCodes);
```

## 🎯 成功标志

当一切正常工作时，你应该看到：
1. ✅ 无语法错误
2. ✅ 无重复注入
3. ✅ 自动填充兑换码
4. ✅ 自动点击继续按钮
5. ✅ 检测到错误消息
6. ✅ 自动切换到下一个兑换码
7. ✅ 显示切换通知

现在应该可以完全自动化验证兑换码了！🎉
