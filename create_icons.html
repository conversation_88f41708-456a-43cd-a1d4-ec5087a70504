
<!DOCTYPE html>
<html>
<head>
    <title>Create Icons</title>
</head>
<body>
    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Draw background
            ctx.fillStyle = '#1F1F1F';
            ctx.fillRect(0, 0, size, size);
            
            // Draw white P-like shape (simplified Perplexity logo)
            ctx.fillStyle = '#FFFFFF';
            const scale = size / 128;
            
            // Simple P shape
            ctx.fillRect(20 * scale, 20 * scale, 60 * scale, 10 * scale);
            ctx.fillRect(20 * scale, 20 * scale, 10 * scale, 88 * scale);
            ctx.fillRect(20 * scale, 50 * scale, 40 * scale, 10 * scale);
            ctx.fillRect(50 * scale, 30 * scale, 10 * scale, 30 * scale);
            
            // Download the icon
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // Create icons
        [16, 48, 128].forEach(size => {
            setTimeout(() => createIcon(size), 100 * size);
        });
        
        document.body.innerHTML = '<h1>Icons created! Check your downloads folder.</h1>';
    </script>
</body>
</html>
    