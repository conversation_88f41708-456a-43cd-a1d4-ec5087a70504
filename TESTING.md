# 测试指南

## 更新说明

根据Perplexity兑换页面的实际HTML结构，插件已经进行了以下优化：

### 🎯 针对性改进

1. **精确的元素选择器**
   - 优先查找 `input[placeholder="优惠码"]`
   - 支持 `input.font-mono` 类选择器
   - 智能识别表单中的文本输入框

2. **增强的输入模拟**
   - 逐字符模拟键盘输入
   - 触发完整的事件序列（keydown → input → keyup）
   - 支持现代React/Vue应用的事件处理

3. **智能结果检测**
   - 检测成功图标 `svg[data-icon="circle-check"]`
   - 识别"优惠码已应用"文本
   - 监控页面opacity变化

4. **调试模式**
   - 可视化调试面板
   - 实时状态监控
   - 详细的操作日志

## 🧪 测试步骤

### 1. 基础安装测试

```bash
# 1. 确保所有文件存在
ls -la manifest.json popup.html popup.css popup.js content.js background.js icons/

# 2. 检查文件权限
chmod 644 *.js *.html *.css *.json
chmod 644 icons/*
```

### 2. Chrome扩展安装

1. 打开 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择插件文件夹
5. 确认插件图标出现在工具栏

### 3. 功能测试

#### 3.1 基础界面测试
- [ ] 点击插件图标，弹窗正常显示
- [ ] 所有按钮和输入框正常显示
- [ ] 帮助按钮显示快捷键信息
- [ ] 调试模式复选框可以切换

#### 3.2 数据管理测试
```
测试兑换码：
TEST123ABC
DEMO456DEF
SAMPLE789GHI
```

- [ ] 输入测试兑换码并保存
- [ ] 关闭浏览器重新打开，数据仍存在
- [ ] 清空功能正常工作
- [ ] 剩余兑换码计数正确

#### 3.3 页面交互测试

1. **打开目标页面**
   ```
   https://www.perplexity.ai/join/p/priority
   ```

2. **开启调试模式**
   - 在插件中勾选"显示调试信息"
   - 刷新Perplexity页面
   - 确认调试面板出现

3. **测试自动填充**
   - 点击"开始验证"
   - 观察调试面板中的日志
   - 确认兑换码已填充到输入框
   - 检查输入框值是否正确

4. **测试结果检测**
   - 手动点击"继续"按钮提交
   - 观察页面变化
   - 检查插件是否正确检测到结果

### 4. 错误处理测试

#### 4.1 网络错误测试
- 断开网络连接
- 尝试使用插件
- 确认错误提示正确显示

#### 4.2 页面结构变化测试
- 在非Perplexity页面使用插件
- 确认插件正确识别并提示

#### 4.3 无效兑换码测试
- 使用明显无效的兑换码（如"INVALID123"）
- 确认插件正确处理错误状态

### 5. 性能测试

#### 5.1 内存使用
```javascript
// 在浏览器控制台运行
console.log('Memory usage:', performance.memory);
```

#### 5.2 响应时间
- 测试插件响应速度
- 确认无明显延迟
- 检查CPU使用率

### 6. 兼容性测试

#### 6.1 浏览器版本
- Chrome 88+ (Manifest V3支持)
- 测试隐身窗口
- 测试多标签页

#### 6.2 操作系统
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu/Debian)

## 🐛 常见问题排查

### 问题1: 插件无法填充兑换码

**排查步骤:**
1. 开启调试模式查看日志
2. 检查页面是否完全加载
3. 确认在正确的Perplexity页面
4. 查看浏览器控制台错误

**解决方案:**
```javascript
// 在页面控制台手动测试
const input = document.querySelector('input[placeholder="优惠码"]');
console.log('找到输入框:', input);
if (input) {
    input.value = 'TEST123';
    input.dispatchEvent(new Event('input', {bubbles: true}));
}
```

### 问题2: 结果检测不准确

**排查步骤:**
1. 观察页面DOM变化
2. 检查成功/失败元素
3. 查看调试日志

**手动检测:**
```javascript
// 检查成功元素
const successIcon = document.querySelector('svg[data-icon="circle-check"]');
const successText = document.evaluate(
    "//text()[contains(., '优惠码已应用')]",
    document,
    null,
    XPathResult.FIRST_ORDERED_NODE_TYPE,
    null
).singleNodeValue;

console.log('成功图标:', successIcon);
console.log('成功文本:', successText);
```

### 问题3: 调试面板不显示

**检查项目:**
- 调试模式是否开启
- content script是否正确注入
- 页面权限是否正确

## 📊 测试报告模板

```markdown
## 测试报告

**测试日期:** [日期]
**测试环境:** 
- 浏览器: Chrome [版本]
- 操作系统: [系统版本]
- 插件版本: 1.0.0

**测试结果:**

### 功能测试
- [ ] 基础界面 - 通过/失败
- [ ] 数据管理 - 通过/失败  
- [ ] 页面交互 - 通过/失败
- [ ] 错误处理 - 通过/失败

### 性能测试
- 内存使用: [数值]MB
- 响应时间: [数值]ms
- CPU使用: [百分比]%

### 发现的问题
1. [问题描述]
2. [问题描述]

### 建议改进
1. [改进建议]
2. [改进建议]
```

## 🚀 部署前检查清单

- [ ] 所有测试用例通过
- [ ] 性能指标在可接受范围内
- [ ] 错误处理机制完善
- [ ] 用户界面友好
- [ ] 调试功能正常
- [ ] 文档完整准确

## 📞 技术支持

如果在测试过程中遇到问题：

1. **查看调试日志** - 开启调试模式获取详细信息
2. **检查浏览器控制台** - 查看JavaScript错误
3. **验证页面结构** - 确认Perplexity页面没有重大更新
4. **重新安装插件** - 清除缓存后重新安装

测试完成后，插件应该能够稳定可靠地在Perplexity兑换页面上工作。
