# Perplexity Code Validator

一个Chrome浏览器插件，用于自动化验证Perplexity兑换码的过程。

## 功能特性

- 🔄 **批量管理兑换码** - 一次性输入多个兑换码
- ⚡ **自动填充** - 自动填充兑换码到Perplexity页面
- 🗑️ **智能清理** - 自动移除无效的兑换码
- 💾 **数据持久化** - 保存未验证的兑换码到本地存储
- 🕵️ **隐身窗口支持** - 支持在隐身窗口中使用
- 📊 **状态跟踪** - 实时显示验证状态和剩余兑换码数量

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹
6. 插件安装完成

### 方法二：生成图标（可选）

如果需要自定义图标：

1. 在浏览器中打开 `generate-icons.html`
2. 图标文件会自动下载到下载文件夹
3. 将下载的 `icon16.png`、`icon48.png`、`icon128.png` 移动到 `icons/` 文件夹

## 使用方法

### 基本使用流程

1. **准备兑换码**
   - 点击Chrome工具栏中的插件图标
   - 在文本框中输入所有兑换码（每行一个）
   - 点击"保存兑换码"

2. **开始验证**
   - 打开 [Perplexity兑换页面](https://www.perplexity.ai/join/p/priority)
   - 在插件弹窗中点击"开始验证"
   - 插件会自动填充第一个兑换码

3. **处理验证结果**
   - 手动点击页面上的提交按钮进行验证
   - 如果兑换码无效，点击插件中的"下一个兑换码"
   - 如果兑换码有效，插件会自动保留该兑换码并停止验证

4. **继续验证**
   - 无效的兑换码会自动从列表中移除
   - 重复步骤2-3直到所有兑换码验证完成

### 高级功能

- **数据持久化**：兑换码会自动保存到本地，关闭浏览器后重新打开仍然保留
- **状态监控**：实时显示当前验证状态和剩余兑换码数量
- **隐身窗口**：插件在隐身窗口中同样可以正常工作
- **错误处理**：自动检测页面错误并提供相应提示

## 文件结构

```
perplexity-code-validator/
├── manifest.json          # 插件配置文件
├── popup.html             # 插件弹窗界面
├── popup.css              # 弹窗样式文件
├── popup.js               # 弹窗逻辑脚本
├── content.js             # 页面内容脚本
├── background.js          # 后台服务脚本
├── generate-icons.html    # 图标生成工具
├── icons/                 # 图标文件夹
│   ├── icon16.png
│   ├── icon48.png
│   ├── icon128.png
│   └── icon.svg
└── README.md              # 说明文档
```

## 技术特性

- **Manifest V3** - 使用最新的Chrome扩展API
- **Content Scripts** - 与网页进行安全交互
- **Chrome Storage API** - 可靠的数据存储
- **消息传递** - 组件间高效通信
- **DOM监听** - 智能检测页面变化

## 故障排除

### 常见问题

1. **插件无法填充兑换码**
   - 确保在正确的Perplexity兑换页面
   - 刷新页面后重试
   - 检查浏览器控制台是否有错误信息

2. **兑换码没有保存**
   - 确保点击了"保存兑换码"按钮
   - 检查浏览器是否允许插件访问存储

3. **页面检测失败**
   - 确保页面完全加载完成
   - 尝试手动刷新页面
   - 检查网络连接

### 调试模式

1. 打开Chrome开发者工具（F12）
2. 切换到"Console"标签
3. 查看插件相关的日志信息
4. 如有错误，请记录错误信息

## 更新日志

### v1.0.0
- 初始版本发布
- 基本的兑换码管理功能
- 自动填充和验证功能
- 数据持久化存储

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个插件。

## 免责声明

本插件仅用于学习和个人使用目的。请遵守Perplexity的服务条款和使用政策。
