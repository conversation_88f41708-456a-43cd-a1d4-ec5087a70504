# Perplexity Code Validator - 安装指南

## 快速安装

### 步骤 1: 下载插件文件
确保你有以下文件：
- `manifest.json`
- `popup.html`
- `popup.css`
- `popup.js`
- `content.js`
- `background.js`
- `icons/` 文件夹（包含图标文件）

### 步骤 2: 安装到Chrome
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择包含所有插件文件的文件夹
6. 插件安装完成！

### 步骤 3: 验证安装
- 在Chrome工具栏中应该能看到插件图标
- 点击图标应该能打开插件弹窗
- 如果看不到图标，检查插件是否已启用

## 使用方法

### 基本流程
1. **准备兑换码**
   - 点击插件图标打开弹窗
   - 在文本框中输入兑换码（每行一个）
   - 点击"保存兑换码"

2. **开始验证**
   - 打开 [Perplexity兑换页面](https://www.perplexity.ai/join/p/priority)
   - 在插件中点击"开始验证"
   - 插件会自动填充第一个兑换码

3. **处理结果**
   - 手动点击页面上的提交按钮
   - 如果兑换码无效，点击"下一个兑换码"
   - 如果有效，插件会自动保留该兑换码

### 快捷键
- `Ctrl+S` (Mac: `Cmd+S`): 保存兑换码
- `Ctrl+Enter` (Mac: `Cmd+Enter`): 开始验证
- `Ctrl+N` (Mac: `Cmd+N`): 下一个兑换码

### 高级功能
- **自动保存**: 输入兑换码后会自动保存
- **数据持久化**: 兑换码会保存到本地，重启浏览器后仍然存在
- **隐身窗口支持**: 可以在隐身窗口中正常使用
- **智能清理**: 无效的兑换码会自动从列表中移除

## 故障排除

### 常见问题

**Q: 插件无法填充兑换码**
A: 
- 确保在正确的Perplexity兑换页面
- 刷新页面后重试
- 检查浏览器控制台是否有错误

**Q: 兑换码没有保存**
A:
- 确保点击了"保存兑换码"按钮
- 检查浏览器是否允许插件访问存储

**Q: 插件图标不显示**
A:
- 检查插件是否已启用
- 尝试重新加载插件
- 确保所有文件都在正确位置

### 调试模式
1. 右键点击插件图标 → "检查弹出内容"
2. 打开开发者工具查看控制台日志
3. 在Perplexity页面按F12查看content script日志

## 更新插件

如果有新版本：
1. 下载新的插件文件
2. 在 `chrome://extensions/` 中点击插件的"重新加载"按钮
3. 或者删除旧版本，重新安装新版本

## 卸载插件

1. 访问 `chrome://extensions/`
2. 找到"Perplexity Code Validator"
3. 点击"移除"按钮
4. 确认删除

## 技术支持

如果遇到问题：
1. 检查浏览器控制台错误信息
2. 确保Chrome版本支持Manifest V3
3. 验证所有文件完整性

## 安全说明

- 插件只在Perplexity兑换页面运行
- 兑换码仅保存在本地浏览器中
- 不会向外部服务器发送任何数据
- 开源代码，可自行审查安全性

## 许可证

MIT License - 可自由使用和修改
