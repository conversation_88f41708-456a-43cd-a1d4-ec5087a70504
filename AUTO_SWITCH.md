# 🔄 自动切换功能说明

## 功能概述

新增的自动切换功能可以在检测到兑换码无效后，自动切换到下一个兑换码并继续验证，实现完全无人值守的验证流程。

## ✨ 主要特性

### 1. 智能错误检测
- **精确识别**: 根据页面实际HTML结构检测错误消息
- **多种错误**: 支持检测"无效的促销代码"等各种错误提示
- **实时监控**: 使用MutationObserver实时监控页面变化

### 2. 自动切换流程
- **错误检测** → **自动切换** → **填充新兑换码** → **自动提交** → **继续监控**
- **视觉提示**: 显示漂亮的通知提示用户已自动切换
- **智能延迟**: 1.5秒延迟确保错误消息完全显示

### 3. 用户控制
- **可选功能**: 用户可以开启或关闭自动切换
- **默认开启**: 新用户默认开启自动切换功能
- **独立控制**: 与自动点击功能独立，可单独控制

## 🔧 技术实现

### 错误检测逻辑
```javascript
// 检测错误消息
const errorTextElements = document.querySelectorAll('.text-superAlt, [class*="text-red"], [class*="error"]');
for (const element of errorTextElements) {
    const errorText = element.textContent.trim();
    if (errorText && 
        (errorText.includes('无效的促销代码') || 
         errorText.includes('无效') || 
         errorText.includes('请尝试复制并粘贴代码')) &&
        element.offsetParent !== null) {
        
        // 检测到错误，触发自动切换
        if (autoSwitchMode && allCodes && allCodes.length > 1) {
            setTimeout(async () => {
                await handleNextCode();
                showAutoNextNotification();
            }, 1500);
        }
    }
}
```

### 自动切换流程
```javascript
// 处理下一个兑换码
async function handleNextCode() {
    // 移除当前无效兑换码
    const removedCode = allCodes.splice(currentCodeIndex, 1)[0];
    
    // 填充下一个兑换码
    const nextCode = allCodes[currentCodeIndex];
    await fillCode(nextCode);
    
    // 如果开启自动点击，自动提交
    if (autoClickMode) {
        setTimeout(async () => {
            await autoClickContinueButton();
        }, 800);
    }
}
```

### 视觉通知
```javascript
// 显示自动切换通知
function showAutoNextNotification() {
    const notification = document.createElement('div');
    notification.innerHTML = `
        <div style="...漂亮的通知样式...">
            <div>⚡ 兑换码无效</div>
            <div>已自动切换到下一个兑换码</div>
        </div>
    `;
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => notification.remove(), 3000);
}
```

## 📋 使用方法

### 开启/关闭自动切换
1. **打开插件弹窗**
2. **找到设置区域** → "无效时自动切换下一个"复选框
3. **勾选开启** → 自动切换功能启用
4. **取消勾选** → 自动切换功能关闭

### 完全自动化验证流程
当同时开启"自动点击继续按钮"和"无效时自动切换下一个"时：

1. **输入兑换码** → 在插件中输入所有兑换码
2. **开始验证** → 点击"开始验证"
3. **自动流程** → 
   - 自动填充兑换码
   - 自动点击继续按钮
   - 自动检测验证结果
   - 如果无效，自动切换到下一个
   - 如果有效，停止并显示成功
4. **无人值守** → 整个过程无需人工干预

### 半自动模式
如果只开启自动切换，关闭自动点击：
1. 自动填充兑换码
2. 需要手动点击"继续"按钮
3. 如果无效，自动切换到下一个兑换码
4. 重复步骤2-3

## 🎯 使用场景

### 适合自动切换的场景
- **大量兑换码**: 有很多兑换码需要验证
- **无人值守**: 希望插件自动运行
- **效率优先**: 追求最快的验证速度
- **批量处理**: 需要处理大批量兑换码

### 适合手动切换的场景
- **少量兑换码**: 只有几个兑换码需要验证
- **谨慎验证**: 需要人工确认每个结果
- **学习观察**: 想要观察验证过程
- **网络不稳定**: 担心自动切换过快

## 🛡️ 安全考虑

### 切换频率控制
- **智能延迟**: 1.5秒延迟确保错误消息完全显示
- **检测冷却**: 2秒冷却时间防止重复检测
- **状态管理**: 防止重复触发切换

### 错误检测准确性
- **精确匹配**: 只检测特定的错误消息文本
- **元素可见性**: 确保错误元素真正可见
- **多重验证**: 使用多种选择器确保准确性

### 用户控制
- **随时关闭**: 用户可以随时关闭自动切换
- **独立控制**: 与其他功能独立，不相互影响
- **状态保存**: 设置会保存到本地存储

## 🔍 调试和监控

### 调试信息
开启调试模式可以看到：
```
[14:30:15] 自动切换模式: 开启
[14:30:16] 检测到错误消息: 无效的促销代码。请尝试复制并粘贴代码，并检查是否有任何拼写错误。
[14:30:16] 自动切换到下一个兑换码
[14:30:17] 移除无效兑换码: PPLXO29QXG5V0I
[14:30:17] 填充下一个兑换码: PPLXO2FNO3B6ZR
[14:30:18] 显示自动切换提示
```

### 视觉反馈
- **通知提示**: 右上角显示漂亮的切换通知
- **调试面板**: 详细的操作日志
- **状态更新**: 实时显示当前兑换码和剩余数量

## 🚨 注意事项

### 使用建议
1. **网络稳定**: 确保网络连接稳定
2. **观察通知**: 注意观察自动切换通知
3. **合理设置**: 根据需要开启或关闭功能

### 可能的问题
1. **检测延迟**: 网络慢时可能需要更长检测时间
2. **页面变化**: Perplexity页面结构变化可能影响检测
3. **误检测**: 极少数情况下可能误检测错误消息

### 故障排除
1. **关闭自动切换**: 如果遇到问题，可以关闭自动切换
2. **检查调试**: 开启调试模式查看详细检测过程
3. **手动验证**: 可以随时切换到手动模式

## 📈 效率提升

### 完全自动化模式
- **传统方式**: 需要持续监控和手动操作
- **自动切换**: 完全无人值守，自动处理所有兑换码
- **时间节省**: 可以做其他事情，让插件自动运行

### 错误处理效率
- **手动模式**: 需要人工判断错误并手动切换
- **自动模式**: 自动识别错误并立即切换
- **准确性**: 基于页面实际内容，检测准确率高

## 🔄 与其他功能的配合

### 功能组合建议
1. **批量检测 + 自动切换**: 先筛选，再自动验证
2. **自动点击 + 自动切换**: 完全自动化验证
3. **调试模式 + 自动切换**: 监控自动切换过程

### 最佳实践
1. **大量兑换码**: 开启所有自动化功能
2. **少量兑换码**: 可以关闭自动切换，保持手动控制
3. **首次使用**: 建议开启调试模式观察过程

这个自动切换功能让兑换码验证过程真正实现了无人值守，大大提高了处理大量兑换码的效率。
