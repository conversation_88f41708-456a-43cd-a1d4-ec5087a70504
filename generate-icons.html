<!DOCTYPE html>
<html>
<head>
    <title>Generate Icons</title>
</head>
<body>
    <h1>Icon Generator</h1>
    <p>Open browser console and run the script to generate PNG icons.</p>
    
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <script>
        function generateIcons() {
            const sizes = [16, 48, 128];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                
                // 清空画布
                ctx.clearRect(0, 0, size, size);
                
                // 创建渐变
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#4A90E2');
                gradient.addColorStop(1, '#357ABD');
                
                // 绘制背景圆形
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
                ctx.fillStyle = gradient;
                ctx.fill();
                ctx.strokeStyle = '#2C5282';
                ctx.lineWidth = 1;
                ctx.stroke();
                
                // 绘制代码符号
                ctx.fillStyle = 'white';
                ctx.font = `bold ${size/5}px monospace`;
                ctx.textAlign = 'center';
                ctx.fillText('</>', size/2, size/2 - size/8);
                
                // 绘制验证勾号
                ctx.strokeStyle = 'white';
                ctx.lineWidth = size/32;
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';
                ctx.beginPath();
                ctx.moveTo(size * 0.35, size * 0.58);
                ctx.lineTo(size * 0.43, size * 0.66);
                ctx.lineTo(size * 0.66, size * 0.43);
                ctx.stroke();
                
                // 装饰性元素
                ctx.fillStyle = 'rgba(255,255,255,0.3)';
                ctx.beginPath();
                ctx.arc(size * 0.23, size * 0.23, size * 0.02, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.beginPath();
                ctx.arc(size * 0.77, size * 0.27, size * 0.015, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.beginPath();
                ctx.arc(size * 0.74, size * 0.74, size * 0.02, 0, 2 * Math.PI);
                ctx.fill();
                
                // 下载图标
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        // 自动生成图标
        setTimeout(generateIcons, 1000);
        
        console.log('Icons will be generated automatically. Check your downloads folder.');
    </script>
</body>
</html>
