# 部署和测试指南

## 部署前检查清单

### 文件完整性检查
确保以下文件存在且内容正确：

- [ ] `manifest.json` - 插件配置文件
- [ ] `popup.html` - 用户界面
- [ ] `popup.css` - 界面样式
- [ ] `popup.js` - 界面逻辑
- [ ] `content.js` - 页面交互脚本
- [ ] `background.js` - 后台服务
- [ ] `icons/icon16.png` - 16x16图标
- [ ] `icons/icon48.png` - 48x48图标
- [ ] `icons/icon128.png` - 128x128图标
- [ ] `README.md` - 使用说明
- [ ] `INSTALL.md` - 安装指南

### 功能测试清单

#### 基础功能测试
- [ ] 插件能正常加载
- [ ] 弹窗界面正常显示
- [ ] 兑换码输入和保存功能
- [ ] 数据持久化（重启浏览器后数据仍存在）

#### 页面交互测试
- [ ] 在Perplexity兑换页面能正常工作
- [ ] 自动填充兑换码功能
- [ ] 页面元素检测正常
- [ ] 错误处理机制

#### 用户体验测试
- [ ] 快捷键功能正常
- [ ] 状态指示器工作正常
- [ ] 消息提示显示正确
- [ ] 响应式设计在不同窗口大小下正常

## 测试步骤

### 1. 安装测试
```bash
# 1. 打开Chrome浏览器
# 2. 访问 chrome://extensions/
# 3. 开启开发者模式
# 4. 点击"加载已解压的扩展程序"
# 5. 选择插件文件夹
```

### 2. 基础功能测试
1. 点击插件图标，确认弹窗正常显示
2. 输入测试兑换码：
   ```
   TEST123
   DEMO456
   SAMPLE789
   ```
3. 点击"保存兑换码"，确认保存成功
4. 关闭浏览器重新打开，确认数据仍存在

### 3. 页面交互测试
1. 打开 https://www.perplexity.ai/join/p/priority
2. 在插件中点击"开始验证"
3. 确认兑换码已自动填充到页面输入框
4. 测试"下一个兑换码"功能

### 4. 错误处理测试
1. 在非Perplexity页面测试插件行为
2. 测试网络错误情况
3. 测试无效兑换码处理

## 性能优化

### 代码优化
- 使用事件委托减少内存占用
- 避免不必要的DOM查询
- 优化消息传递频率

### 存储优化
- 定期清理无效数据
- 压缩存储的JSON数据
- 设置存储大小限制

## 安全考虑

### 权限最小化
- 只请求必要的权限
- 限制host_permissions范围
- 避免使用危险的API

### 数据安全
- 兑换码仅本地存储
- 不向外部服务器发送数据
- 使用HTTPS连接

## 发布准备

### 版本管理
1. 更新 `manifest.json` 中的版本号
2. 更新 `README.md` 中的更新日志
3. 创建发布标签

### 打包
```bash
# 创建发布包
zip -r perplexity-code-validator-v1.0.0.zip \
  manifest.json \
  popup.html \
  popup.css \
  popup.js \
  content.js \
  background.js \
  icons/ \
  README.md \
  INSTALL.md
```

### Chrome Web Store发布（可选）
1. 注册Chrome开发者账户
2. 准备应用截图和描述
3. 上传插件包
4. 等待审核

## 维护和更新

### 监控
- 定期检查Perplexity页面结构变化
- 监控用户反馈
- 跟踪Chrome API更新

### 更新流程
1. 修复bug或添加新功能
2. 更新版本号
3. 测试所有功能
4. 发布新版本

## 故障排除

### 常见问题解决
1. **插件无法加载**
   - 检查manifest.json语法
   - 确认所有文件路径正确
   - 查看Chrome扩展页面的错误信息

2. **页面交互失败**
   - 检查content script是否正确注入
   - 确认页面选择器是否有效
   - 查看浏览器控制台错误

3. **数据丢失**
   - 检查storage权限
   - 确认存储API调用正确
   - 验证数据序列化/反序列化

## 技术支持

### 调试工具
- Chrome DevTools
- Extension DevTools
- Console日志

### 日志记录
在开发模式下启用详细日志：
```javascript
const DEBUG = true;
if (DEBUG) console.log('Debug info:', data);
```

## 备份和恢复

### 用户数据备份
提供导出/导入功能让用户备份兑换码：
```javascript
// 导出数据
const exportData = () => {
  chrome.storage.local.get(['codes'], (result) => {
    const dataStr = JSON.stringify(result.codes);
    // 创建下载链接
  });
};
```

这个部署指南确保插件能够稳定可靠地运行，并为后续维护提供了完整的流程。
