#!/bin/bash

# 去重脚本 - 去除code.md中的重复兑换码
# 使用方法: ./dedupe_codes.sh

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输入和输出文件
INPUT_FILE="code.md"
OUTPUT_FILE="code_unique.md"
BACKUP_FILE="code_backup.md"

echo -e "${BLUE}🔍 兑换码去重工具${NC}"
echo "=================================="

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo -e "${RED}❌ 错误: 找不到 $INPUT_FILE 文件${NC}"
    exit 1
fi

# 显示原始统计信息
ORIGINAL_COUNT=$(wc -l < "$INPUT_FILE")
echo -e "${YELLOW}📊 原始文件统计:${NC}"
echo "   文件: $INPUT_FILE"
echo "   总行数: $ORIGINAL_COUNT"

# 创建备份
echo -e "\n${BLUE}💾 创建备份...${NC}"
cp "$INPUT_FILE" "$BACKUP_FILE"
echo "   备份文件: $BACKUP_FILE"

# 去除重复行，保持原始顺序
echo -e "\n${BLUE}🔄 正在去除重复...${NC}"

# 方法1: 使用awk去重（保持首次出现的顺序）
awk '!seen[$0]++' "$INPUT_FILE" > "$OUTPUT_FILE"

# 统计去重后的结果
UNIQUE_COUNT=$(wc -l < "$OUTPUT_FILE")
DUPLICATE_COUNT=$((ORIGINAL_COUNT - UNIQUE_COUNT))

echo -e "\n${GREEN}✅ 去重完成!${NC}"
echo "=================================="
echo -e "${YELLOW}📈 统计结果:${NC}"
echo "   原始兑换码数量: $ORIGINAL_COUNT"
echo "   去重后数量: $UNIQUE_COUNT"
echo "   重复数量: $DUPLICATE_COUNT"
echo "   去重率: $(echo "scale=2; $DUPLICATE_COUNT * 100 / $ORIGINAL_COUNT" | bc -l)%"

echo -e "\n${YELLOW}📁 文件信息:${NC}"
echo "   原始文件: $INPUT_FILE (已备份为 $BACKUP_FILE)"
echo "   去重文件: $OUTPUT_FILE"

# 显示重复的兑换码
echo -e "\n${BLUE}🔍 重复的兑换码:${NC}"
echo "=================================="

# 找出重复的兑换码
sort "$INPUT_FILE" | uniq -d > duplicates.txt

if [ -s duplicates.txt ]; then
    echo -e "${RED}发现以下重复兑换码:${NC}"
    cat duplicates.txt | while read -r code; do
        count=$(grep -c "^$code$" "$INPUT_FILE")
        echo "   $code (出现 $count 次)"
    done
else
    echo -e "${GREEN}没有发现重复的兑换码${NC}"
fi

# 清理临时文件
rm -f duplicates.txt

echo -e "\n${GREEN}🎉 处理完成!${NC}"
echo -e "${YELLOW}💡 提示:${NC}"
echo "   - 如果结果正确，可以用 $OUTPUT_FILE 替换原文件"
echo "   - 如果需要恢复，可以使用备份文件 $BACKUP_FILE"
echo ""
echo -e "${BLUE}替换原文件的命令:${NC}"
echo "   mv $OUTPUT_FILE $INPUT_FILE"
echo ""
echo -e "${BLUE}查看去重结果:${NC}"
echo "   head -20 $OUTPUT_FILE"
