# 🎯 手动验证模式指南

## 问题解决

你遇到的问题已经修复：

### 1. CSS选择器错误
- **问题**: `:contains()` 不是标准CSS选择器
- **修复**: 改用XPath查询

### 2. 自动切换过快
- **问题**: 100多个兑换码瞬间验证完
- **修复**: 默认关闭自动切换，改为手动验证模式

## 🔧 现在的默认设置

### 默认配置
- ✅ **自动点击继续按钮**: 开启
- ❌ **无效时自动切换下一个**: 关闭（默认）
- ❌ **显示调试信息**: 关闭

### 手动验证流程
1. **填充兑换码** → 插件自动填充
2. **自动点击继续** → 插件自动点击
3. **查看结果** → 你手动查看验证结果
4. **手动切换** → 点击"下一个"按钮切换到下一个兑换码

## 🎮 使用模式选择

### 模式1: 手动验证（推荐）
**适合**: 想要控制验证节奏，一个个查看结果

**设置**:
- ✅ 自动点击继续按钮
- ❌ 无效时自动切换下一个

**流程**:
```
1. 插件填充兑换码
2. 插件自动点击继续
3. 你查看验证结果
4. 如果无效，你手动点击"下一个"
5. 重复步骤1-4
```

### 模式2: 半自动验证
**适合**: 想要看到每个结果，但希望无效时自动切换

**设置**:
- ✅ 自动点击继续按钮
- ✅ 无效时自动切换下一个

**流程**:
```
1. 插件填充兑换码
2. 插件自动点击继续
3. 如果无效，插件自动切换到下一个
4. 如果有效，停止并显示成功
```

### 模式3: 完全手动
**适合**: 想要完全控制每个步骤

**设置**:
- ❌ 自动点击继续按钮
- ❌ 无效时自动切换下一个

**流程**:
```
1. 插件填充兑换码
2. 你手动点击继续按钮
3. 你查看验证结果
4. 你手动点击"下一个"切换
```

## 📋 推荐的验证步骤

### 对于你的75个兑换码

1. **设置插件**:
   ```
   ✅ 自动点击继续按钮
   ❌ 无效时自动切换下一个  (保持关闭)
   ✅ 显示调试信息 (可选，用于调试)
   ```

2. **开始验证**:
   ```
   - 输入你的75个兑换码
   - 点击"开始验证"
   - 插件会填充第一个兑换码并自动点击继续
   ```

3. **查看结果**:
   ```
   - 如果显示"优惠码已应用" → 成功！
   - 如果显示"无效的促销代码" → 失败，点击"下一个"
   ```

4. **继续验证**:
   ```
   - 手动点击页面上的"下一个"按钮
   - 插件会填充下一个兑换码
   - 重复步骤3-4
   ```

## 🔍 页面上的"下一个"按钮

插件会在页面上添加一个"下一个"按钮：

```
[继续] [下一个]
```

- **继续**: Perplexity原有的按钮
- **下一个**: 插件添加的按钮，点击切换到下一个兑换码

## 🎯 验证结果识别

### 成功状态
- 页面会显示成功的覆盖层
- 显示"优惠码已应用"文字
- 通常有绿色的勾选图标

### 失败状态
- 显示"无效的促销代码"
- 提示"请尝试复制并粘贴代码，并检查是否有任何拼写错误"
- 输入框可能会变红或显示错误样式

## 🚀 快速测试

### 测试手动模式
1. **重新加载插件**
2. **确认设置**: 自动切换应该是关闭的
3. **输入几个测试兑换码**:
   ```
   PPLXO23ZQHWR4Y
   PPLXO20I4H0815
   PPLXO2O2211CWY
   ```
4. **开始验证**: 应该只验证第一个，然后等待你手动点击"下一个"

## 🔧 如果需要批量自动验证

如果你想要无人值守的批量验证：

1. **开启自动切换**:
   ```
   ✅ 自动点击继续按钮
   ✅ 无效时自动切换下一个
   ```

2. **开始验证**: 插件会自动处理所有兑换码

3. **监控过程**: 可以开启调试信息查看详细过程

## 💡 使用建议

### 首次使用
- 建议使用手动模式，熟悉验证流程
- 可以开启调试信息观察插件行为

### 大量兑换码
- 可以先用批量检测功能筛选
- 然后用手动模式验证筛选后的兑换码

### 网络不稳定
- 建议使用手动模式
- 可以控制验证节奏，避免请求过快

现在插件默认是手动验证模式，你可以一个个慢慢验证兑换码了！🎉
