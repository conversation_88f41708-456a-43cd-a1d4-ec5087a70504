// DOM元素
const codesInput = document.getElementById('codesInput');
const saveCodesBtn = document.getElementById('saveCodesBtn');
const startValidationBtn = document.getElementById('startValidationBtn');
const nextCodeBtn = document.getElementById('nextCodeBtn');
const clearAllBtn = document.getElementById('clearAllBtn');
const currentStatus = document.getElementById('currentStatus');
const remainingCodes = document.getElementById('remainingCodes');
const currentCodeDisplay = document.getElementById('currentCode');
const helpBtn = document.getElementById('helpBtn');
const debugModeCheckbox = document.getElementById('debugMode');
const autoClickModeCheckbox = document.getElementById('autoClickMode');
const autoSwitchModeCheckbox = document.getElementById('autoSwitchMode');
const resetDetectionBtn = document.getElementById('resetDetectionBtn');
const batchCheckBtn = document.getElementById('batchCheckBtn');
const batchSizeSelect = document.getElementById('batchSize');
const batchProgress = document.getElementById('batchProgress');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const batchResults = document.getElementById('batchResults');
const validCount = document.getElementById('validCount');
const invalidCount = document.getElementById('invalidCount');

// 状态管理
let codes = [];
let currentCodeIndex = 0;
let isValidating = false;
let isBatchChecking = false;

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    await loadSavedCodes();
    await loadDebugMode();
    await loadAutoClickMode();
    await loadAutoSwitchMode();
    updateUI();
});

// 加载保存的兑换码
async function loadSavedCodes() {
    try {
        const result = await chrome.storage.local.get(['codes', 'currentCodeIndex']);
        codes = result.codes || [];
        currentCodeIndex = result.currentCodeIndex || 0;
        
        if (codes.length > 0) {
            codesInput.value = codes.join('\n');
        }
    } catch (error) {
        console.error('加载兑换码失败:', error);
    }
}

// 保存兑换码到存储
async function saveCodesToStorage() {
    try {
        await chrome.storage.local.set({
            codes: codes,
            currentCodeIndex: currentCodeIndex
        });
    } catch (error) {
        console.error('保存兑换码失败:', error);
    }
}

// 加载调试模式设置
async function loadDebugMode() {
    try {
        const result = await chrome.storage.local.get(['debugMode']);
        const isDebugMode = result.debugMode || false;
        debugModeCheckbox.checked = isDebugMode;
    } catch (error) {
        console.error('加载调试模式设置失败:', error);
    }
}

// 保存调试模式设置
async function saveDebugMode() {
    try {
        await chrome.storage.local.set({
            debugMode: debugModeCheckbox.checked
        });

        // 通知content script调试模式变化
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab && tab.url && tab.url.includes('perplexity.ai')) {
            chrome.tabs.sendMessage(tab.id, {
                action: 'setDebugMode',
                enabled: debugModeCheckbox.checked
            }).catch(() => {
                // 忽略错误，可能页面还没加载content script
            });
        }
    } catch (error) {
        console.error('保存调试模式设置失败:', error);
    }
}

// 加载自动点击模式设置
async function loadAutoClickMode() {
    try {
        const result = await chrome.storage.local.get(['autoClickMode']);
        const isAutoClickMode = result.autoClickMode !== false; // 默认为true
        autoClickModeCheckbox.checked = isAutoClickMode;
    } catch (error) {
        console.error('加载自动点击模式设置失败:', error);
    }
}

// 保存自动点击模式设置
async function saveAutoClickMode() {
    try {
        await chrome.storage.local.set({
            autoClickMode: autoClickModeCheckbox.checked
        });

        // 通知content script自动点击模式变化
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab && tab.url && tab.url.includes('perplexity.ai')) {
            chrome.tabs.sendMessage(tab.id, {
                action: 'setAutoClickMode',
                enabled: autoClickModeCheckbox.checked
            }).catch(() => {
                // 忽略错误，可能页面还没加载content script
            });
        }
    } catch (error) {
        console.error('保存自动点击模式设置失败:', error);
    }
}

// 加载自动切换模式设置
async function loadAutoSwitchMode() {
    try {
        const result = await chrome.storage.local.get(['autoSwitchMode']);
        const isAutoSwitchMode = result.autoSwitchMode !== false; // 默认为true，除非用户主动关闭
        autoSwitchModeCheckbox.checked = isAutoSwitchMode;
    } catch (error) {
        console.error('加载自动切换模式设置失败:', error);
    }
}

// 保存自动切换模式设置
async function saveAutoSwitchMode() {
    try {
        await chrome.storage.local.set({
            autoSwitchMode: autoSwitchModeCheckbox.checked
        });

        // 通知content script自动切换模式变化
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab && tab.url && tab.url.includes('perplexity.ai')) {
            chrome.tabs.sendMessage(tab.id, {
                action: 'setAutoSwitchMode',
                enabled: autoSwitchModeCheckbox.checked
            }).catch(() => {
                // 忽略错误，可能页面还没加载content script
            });
        }
    } catch (error) {
        console.error('保存自动切换模式设置失败:', error);
    }
}

// 更新UI状态
function updateUI() {
    remainingCodes.textContent = codes.length;
    
    if (codes.length > 0 && currentCodeIndex < codes.length) {
        currentCodeDisplay.textContent = codes[currentCodeIndex];
        startValidationBtn.disabled = isBatchChecking;
        batchCheckBtn.disabled = isBatchChecking;
    } else {
        currentCodeDisplay.textContent = '无';
        startValidationBtn.disabled = true;
        batchCheckBtn.disabled = true;
    }

    nextCodeBtn.disabled = !isValidating || currentCodeIndex >= codes.length - 1;
    
    if (isValidating) {
        currentStatus.textContent = '验证中';
        currentStatus.className = 'status working';
    } else if (codes.length === 0) {
        currentStatus.textContent = '无兑换码';
        currentStatus.className = 'status waiting';
    } else {
        currentStatus.textContent = '待机中';
        currentStatus.className = 'status waiting';
    }
}

// 保存兑换码按钮事件
saveCodesBtn.addEventListener('click', async () => {
    const inputText = codesInput.value.trim();
    if (!inputText) {
        alert('请输入兑换码');
        return;
    }
    
    // 解析兑换码，过滤空行
    codes = inputText.split('\n')
        .map(code => code.trim())
        .filter(code => code.length > 0);
    
    currentCodeIndex = 0;
    await saveCodesToStorage();
    updateUI();
    
    showMessage('兑换码已保存', 'success');
});

// 开始验证按钮事件
startValidationBtn.addEventListener('click', async () => {
    if (codes.length === 0) {
        alert('请先保存兑换码');
        return;
    }
    
    // 检查是否在正确的页面
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.url.includes('perplexity.ai/join/p/priority')) {
        const openPage = confirm('需要在Perplexity兑换页面使用此功能。是否打开页面？');
        if (openPage) {
            chrome.tabs.create({ url: 'https://www.perplexity.ai/join/p/priority' });
        }
        return;
    }
    
    isValidating = true;
    updateUI();
    
    // 发送消息到content script
    try {
        await chrome.tabs.sendMessage(tab.id, {
            action: 'fillCode',
            code: codes[currentCodeIndex],
            allCodes: codes,
            currentIndex: currentCodeIndex
        });
        highlightCurrentCode();
        showMessage('已填充兑换码，页面上会显示"下一个"按钮', 'success');
    } catch (error) {
        console.error('发送消息失败:', error);
        showMessage('无法与页面通信，请刷新页面后重试', 'error');
        isValidating = false;
        updateUI();
    }
});

// 下一个兑换码按钮事件
nextCodeBtn.addEventListener('click', async () => {
    if (!isValidating || currentCodeIndex >= codes.length - 1) {
        return;
    }
    
    // 移除当前无效的兑换码
    codes.splice(currentCodeIndex, 1);
    
    // 如果还有兑换码，保持当前索引；否则重置
    if (currentCodeIndex >= codes.length) {
        currentCodeIndex = 0;
        isValidating = false;
    }
    
    // 更新输入框
    codesInput.value = codes.join('\n');
    
    await saveCodesToStorage();
    updateUI();
    
    // 重置页面的验证检测状态
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab && tab.url && tab.url.includes('perplexity.ai')) {
        try {
            await chrome.tabs.sendMessage(tab.id, {
                action: 'resetDetection'
            });
        } catch (error) {
            console.log('无法重置检测状态:', error);
        }
    }

    // 如果还在验证状态且有兑换码，自动填充下一个
    if (isValidating && codes.length > 0) {
        try {
            await chrome.tabs.sendMessage(tab.id, {
                action: 'fillCode',
                code: codes[currentCodeIndex],
                autoClick: autoClickModeCheckbox.checked // 根据设置决定是否自动点击
            });
            highlightCurrentCode();
            const message = autoClickModeCheckbox.checked ?
                '已填充下一个兑换码并自动提交' : '已填充下一个兑换码';
            showMessage(message, 'success');
        } catch (error) {
            console.error('发送消息失败:', error);
            showMessage('无法与页面通信', 'error');
        }
    }
    
    showMessage('已移除无效兑换码', 'success');
});

// 清空所有按钮事件
clearAllBtn.addEventListener('click', async () => {
    if (confirm('确定要清空所有兑换码吗？')) {
        codes = [];
        currentCodeIndex = 0;
        isValidating = false;
        codesInput.value = '';
        
        await chrome.storage.local.clear();
        updateUI();
        showMessage('已清空所有兑换码', 'success');
    }
});

// 帮助按钮事件
helpBtn.addEventListener('click', () => {
    const helpMessage = `快捷键：
• Ctrl+S: 保存兑换码
• Ctrl+Enter: 开始验证
• Ctrl+N: 下一个兑换码

新功能：
• 批量检测：快速筛选有效兑换码
• 页面上会显示"下一个"按钮
• 自动点击继续按钮（可关闭）
• 无效时自动切换下一个（默认开启）
• 直接在页面上切换兑换码
• 无效兑换码自动移除

使用模式：
• 手动验证：关闭自动切换，一个个手动验证
• 自动验证：开启自动切换，无人值守验证
• 批量检测：先筛选有效兑换码再验证
• 支持隐身窗口使用`;

    showMessage(helpMessage, 'info');
});

// 调试模式复选框事件
debugModeCheckbox.addEventListener('change', async () => {
    await saveDebugMode();
    showMessage(
        debugModeCheckbox.checked ? '调试模式已开启' : '调试模式已关闭',
        'info'
    );
});

// 自动点击模式复选框事件
autoClickModeCheckbox.addEventListener('change', async () => {
    await saveAutoClickMode();
    showMessage(
        autoClickModeCheckbox.checked ? '自动点击已开启' : '自动点击已关闭',
        'info'
    );
});

// 自动切换模式复选框事件
autoSwitchModeCheckbox.addEventListener('change', async () => {
    await saveAutoSwitchMode();
    showMessage(
        autoSwitchModeCheckbox.checked ? '自动切换已开启' : '自动切换已关闭',
        'info'
    );
});

// 重置检测状态按钮事件
resetDetectionBtn.addEventListener('click', async () => {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab && tab.url && tab.url.includes('perplexity.ai')) {
        try {
            await chrome.tabs.sendMessage(tab.id, {
                action: 'resetDetection'
            });
            showMessage('已重置检测状态', 'success');
        } catch (error) {
            console.error('重置检测状态失败:', error);
            showMessage('无法与页面通信，请刷新页面', 'error');
        }
    } else {
        showMessage('请在Perplexity页面使用此功能', 'error');
    }
});

// 显示消息
function showMessage(message, type = 'info') {
    // 移除现有的消息
    const existingMessage = document.querySelector('.message-toast');
    if (existingMessage) {
        existingMessage.remove();
    }

    // 创建新的消息元素
    const messageEl = document.createElement('div');
    messageEl.textContent = message;
    messageEl.className = `message-toast ${type}`;

    document.body.appendChild(messageEl);

    // 3秒后移除消息
    setTimeout(() => {
        if (messageEl.parentNode) {
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }
    }, 3000);
}

// 高亮当前兑换码
function highlightCurrentCode() {
    currentCodeDisplay.classList.add('highlight');
    setTimeout(() => {
        currentCodeDisplay.classList.remove('highlight');
    }, 500);
}

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'codeValidated') {
        if (message.success) {
            // 兑换码有效，移除它并停止验证
            codes.splice(currentCodeIndex, 1);
            codesInput.value = codes.join('\n');
            isValidating = false;
            currentCodeIndex = 0;
            saveCodesToStorage();
            updateUI();
            showMessage('兑换码验证成功！', 'success');
        } else {
            // 兑换码无效，显示错误信息
            showMessage(`兑换码无效: ${message.error || '未知错误'}`, 'error');
        }
    }

    if (message.action === 'updateCodes') {
        // 来自页面上"下一个"按钮的兑换码更新
        codes = message.codes;
        currentCodeIndex = message.currentIndex || 0;
        codesInput.value = codes.join('\n');
        saveCodesToStorage();
        updateUI();
        showMessage('已移除无效兑换码', 'info');
    }
});

// 添加键盘快捷键支持
document.addEventListener('keydown', (event) => {
    // Ctrl/Cmd + Enter: 开始验证
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        if (!startValidationBtn.disabled) {
            startValidationBtn.click();
        }
    }

    // Ctrl/Cmd + N: 下一个兑换码
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        if (!nextCodeBtn.disabled) {
            nextCodeBtn.click();
        }
    }

    // Ctrl/Cmd + S: 保存兑换码
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        saveCodesBtn.click();
    }
});

// 添加自动保存功能
let autoSaveTimeout;
codesInput.addEventListener('input', () => {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
        if (codesInput.value.trim()) {
            saveCodesBtn.click();
        }
    }, 2000); // 2秒后自动保存
});

// 批量检测按钮事件
batchCheckBtn.addEventListener('click', async () => {
    if (codes.length === 0) {
        showMessage('请先添加兑换码', 'error');
        return;
    }

    const batchSize = parseInt(batchSizeSelect.value);
    await performBatchCheck(batchSize);
});

// 执行批量检测
async function performBatchCheck(batchSize) {
    isBatchChecking = true;
    updateUI();

    // 显示进度条
    batchProgress.style.display = 'block';
    batchResults.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '准备检测...';

    const totalCodes = codes.length;
    let checkedCount = 0;
    let validCodes = [];
    let invalidCodes = [];

    showMessage('开始批量检测兑换码...', 'info');

    try {
        // 分批处理兑换码
        for (let i = 0; i < totalCodes; i += batchSize) {
            const batch = codes.slice(i, i + batchSize);
            progressText.textContent = `检测中... (${Math.min(i + batchSize, totalCodes)}/${totalCodes})`;

            // 并发检测当前批次
            const batchPromises = batch.map(code => checkSingleCode(code));
            const batchResults = await Promise.allSettled(batchPromises);

            // 处理批次结果
            batchResults.forEach((result, index) => {
                const code = batch[index];
                if (result.status === 'fulfilled' && result.value.isValid) {
                    validCodes.push(code);
                } else {
                    invalidCodes.push(code);
                }
                checkedCount++;

                // 更新进度条
                const progress = (checkedCount / totalCodes) * 100;
                progressFill.style.width = `${progress}%`;
            });

            // 批次间延迟，避免请求过于频繁
            if (i + batchSize < totalCodes) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // 更新兑换码列表，只保留有效的
        codes = validCodes;
        currentCodeIndex = 0;
        codesInput.value = codes.join('\n');
        await saveCodesToStorage();

        // 显示结果
        showBatchResults(validCodes.length, invalidCodes.length);
        showMessage(`检测完成！有效: ${validCodes.length}, 无效: ${invalidCodes.length}`, 'success');

    } catch (error) {
        console.error('批量检测出错:', error);
        showMessage('批量检测出错，请重试', 'error');
    } finally {
        isBatchChecking = false;
        updateUI();

        // 隐藏进度条
        setTimeout(() => {
            batchProgress.style.display = 'none';
        }, 2000);
    }
}

// 检测单个兑换码
async function checkSingleCode(code) {
    try {
        // 获取当前页面的cookies
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        // 发送消息到content script进行API调用
        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'checkCode',
            code: code
        });

        return {
            code: code,
            isValid: response.isValid,
            data: response.data
        };
    } catch (error) {
        console.error(`检测兑换码 ${code} 失败:`, error);
        return {
            code: code,
            isValid: false,
            error: error.message
        };
    }
}

// 显示批量检测结果
function showBatchResults(validCount, invalidCount) {
    document.getElementById('validCount').textContent = `有效: ${validCount}`;
    document.getElementById('invalidCount').textContent = `无效: ${invalidCount}`;
    batchResults.style.display = 'block';
}
