* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    width: 400px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
    position: relative;
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.header h1 {
    font-size: 18px;
    color: #2c3e50;
    font-weight: 600;
}

.help-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    border: 1px solid #007bff;
    border-radius: 50%;
    background: white;
    color: #007bff;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.help-btn:hover {
    background: #007bff;
    color: white;
    transform: scale(1.1);
}

.status-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.status {
    font-weight: 600;
    color: #28a745;
}

.count {
    font-weight: 600;
    color: #007bff;
    font-size: 16px;
}

.input-section {
    margin-bottom: 20px;
}

.input-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

#codesInput {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    resize: vertical;
    min-height: 120px;
    transition: border-color 0.2s;
}

#codesInput:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
}

.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
    grid-column: 1 / -1;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: #138496;
}

.current-code-section {
    margin-bottom: 20px;
    padding: 12px;
    background: #e3f2fd;
    border-radius: 6px;
    border: 1px solid #bbdefb;
}

.current-code-section label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #1565c0;
    font-size: 13px;
}

.current-code {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: 600;
    color: #0d47a1;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #90caf9;
    word-break: break-all;
}

.instructions {
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    font-size: 12px;
}

.instructions h3 {
    margin-bottom: 10px;
    color: #856404;
    font-size: 14px;
}

.instructions ol {
    padding-left: 20px;
    color: #856404;
}

.instructions li {
    margin-bottom: 4px;
}

.instructions a {
    color: #007bff;
    text-decoration: none;
}

.instructions a:hover {
    text-decoration: underline;
}

.debug-section {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #ffeaa7;
}

.debug-section label {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #856404;
    cursor: pointer;
}

.debug-section input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer;
}

/* 状态指示器 */
.status.waiting {
    color: #6c757d;
}

.status.working {
    color: #fd7e14;
    animation: pulse 1.5s ease-in-out infinite;
}

.status.success {
    color: #28a745;
}

.status.error {
    color: #dc3545;
}

/* 动画效果 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按钮悬停效果增强 */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* 输入框焦点效果增强 */
#codesInput:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 13px;
    font-weight: 500;
    z-index: 1000;
    max-width: 250px;
    word-wrap: break-word;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message-toast.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.message-toast.error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.message-toast.info {
    background: linear-gradient(135deg, #007bff, #17a2b8);
}

/* 当前兑换码高亮效果 */
.current-code {
    position: relative;
}

.current-code.highlight {
    animation: highlight 0.5s ease-in-out;
}

@keyframes highlight {
    0% { background-color: #e3f2fd; }
    50% { background-color: #bbdefb; }
    100% { background-color: #e3f2fd; }
}

/* 响应式设计改进 */
@media (max-width: 450px) {
    .container {
        width: 350px;
        padding: 15px;
    }

    .controls {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .btn {
        padding: 12px 16px;
        font-size: 14px;
    }
}

/* 加载状态 */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 批量检测样式 */
.batch-check-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.batch-settings {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.batch-settings label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
}

#batchSize {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    background: white;
}

.batch-progress {
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #17a2b8, #20c997);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    margin-top: 5px;
}

.batch-results {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.results-summary {
    display: flex;
    gap: 15px;
}

.valid-count {
    color: #28a745;
    font-weight: 600;
    font-size: 13px;
}

.invalid-count {
    color: #dc3545;
    font-weight: 600;
    font-size: 13px;
}
